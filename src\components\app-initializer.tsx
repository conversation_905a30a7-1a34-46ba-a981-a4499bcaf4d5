import { useEffect, useState } from 'react';
import { useUserStore } from '@/store';
import { useUserProfile } from '@/hooks/use-user';
import Router from '@/router';

/**
 * 应用初始化组件
 * 负责在应用启动时加载必要的用户数据
 */
export default function AppInitializer() {
    const [isInitialized, setIsInitialized] = useState(false);
    const { userToken } = useUserStore();
    const { getUserInfo } = useUserProfile();

    useEffect(() => {
        const initializeApp = async () => {
            try {
                // 如果有 token，尝试获取用户信息
                if (userToken.token) {
                    await getUserInfo();
                }
            } catch (error) {
                console.error('初始化用户信息失败:', error);
            } finally {
                // 无论成功失败都标记为已初始化
                setIsInitialized(true);
            }
        };

        initializeApp();
    }, [userToken.token, getUserInfo]);

    // 如果还没有初始化完成，抛出 Promise 来触发 Suspense
    if (!isInitialized) {
        throw new Promise((resolve) => {
            setTimeout(resolve, 100); // 最小加载时间
        });
    }

    return <Router />;
}
