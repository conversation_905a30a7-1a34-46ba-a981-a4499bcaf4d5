import { useUserStore } from "@/store";
import { useQuery } from "@tanstack/react-query";
import { getUserProfile } from "@/apis/userService";
import { useMemo } from "react";

/**
 * 支持 Suspense 的用户数据加载 hook
 * 使用 useQuery 的 suspense 模式来触发 Suspense 边界
 */
export const useUserProfileSuspense = () => {
    const setUserProfile = useUserStore((state) => state.setUserProfile);
    const userToken = useUserStore((state) => state.userToken);
    
    // 使用 useQuery 的 suspense 模式
    const { data: userProfile } = useQuery({
        queryKey: ['user-profile'],
        queryFn: async () => {
            const result = await getUserProfile();
            // 将数据同步到 store
            setUserProfile(result.data);
            return result.data;
        },
        enabled: !!userToken.token, // 只有在有 token 时才执行
        staleTime: 5 * 60 * 1000, // 5分钟缓存
        retry: 2,
        // 启用 suspense 模式
        suspense: true,
    });

    const userPermission = useMemo(() => {
        return userProfile?.userRole?.permission;
    }, [userProfile]);

    return {
        userProfile,
        userPermission
    };
};
